"use strict";

const { logger } = require("ee-core/log");
const { getMainWindow } = require("ee-core/electron");
const EventEmitter = require("events");

/**
 * 连接类型枚举
 */
const ConnectionType = {
  WEBSOCKET: "websocket",
  USB: "usb"
};

/**
 * 连接状态枚举
 */
const ConnectionState = {
  DISCONNECTED: "disconnected",
  CONNECTING: "connecting", 
  CONNECTED: "connected",
  RECONNECTING: "reconnecting",
  ERROR: "error"
};

/**
 * 统一连接管理器
 * 管理WebSocket和USB两种连接方式，实现自动切换和故障转移
 */
class ConnectionManager extends EventEmitter {
  static instance = null;

  static getInstance() {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager();
    }
    return ConnectionManager.instance;
  }

  constructor() {
    super();
    if (ConnectionManager.instance) {
      return ConnectionManager.instance;
    }

    // 连接实例
    this.connections = new Map();
    
    // 当前活动连接
    this.activeConnection = null;
    this.activeConnectionType = null;
    
    // 连接优先级 (数字越小优先级越高)
    this.connectionPriority = {
      [ConnectionType.WEBSOCKET]: 1,
      [ConnectionType.USB]: 2
    };
    
    // 连接状态
    this.connectionStates = new Map();
    
    // 健康检查配置
    this.healthCheckInterval = 5000; // 5秒检查一次
    this.healthCheckTimer = null;
    this.connectionTimeout = 10000; // 10秒连接超时
    
    // 重连配置
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.reconnectAttempts = new Map();
    
    // 消息队列 (用于连接切换时的消息缓存)
    this.messageQueue = [];
    this.maxQueueSize = 100;
    
    // 切换锁，防止并发切换
    this.switchingLock = false;
    
    ConnectionManager.instance = this;
    
    this._initializeEventHandlers();
  }

  /**
   * 初始化事件处理器
   */
  _initializeEventHandlers() {
    // 监听连接状态变化
    this.on('connectionStateChanged', this._handleConnectionStateChange.bind(this));
    
    // 监听连接错误
    this.on('connectionError', this._handleConnectionError.bind(this));
    
    // 监听消息接收
    this.on('messageReceived', this._handleMessageReceived.bind(this));
  }

  /**
   * 注册连接实例
   * @param {string} type - 连接类型
   * @param {object} connection - 连接实例
   */
  registerConnection(type, connection) {
    if (!Object.values(ConnectionType).includes(type)) {
      throw new Error(`Invalid connection type: ${type}`);
    }
    
    this.connections.set(type, connection);
    this.connectionStates.set(type, ConnectionState.DISCONNECTED);
    this.reconnectAttempts.set(type, 0);
    
    // 绑定连接事件
    this._bindConnectionEvents(type, connection);
    
    // logger.info(`[ConnectionManager] Registered ${type} connection`);
  }

  /**
   * 绑定连接事件
   * @param {string} type - 连接类型
   * @param {object} connection - 连接实例
   */
  _bindConnectionEvents(type, connection) {
    // 连接成功事件
    connection.on('connected', () => {
      this._updateConnectionState(type, ConnectionState.CONNECTED);
      this.reconnectAttempts.set(type, 0);
      this._tryActivateConnection(type);
    });
    
    // 连接断开事件
    connection.on('disconnected', () => {
      this._updateConnectionState(type, ConnectionState.DISCONNECTED);
      this._handleConnectionLost(type);
    });
    
    // 连接错误事件
    connection.on('error', (error) => {
      this._updateConnectionState(type, ConnectionState.ERROR);
      this.emit('connectionError', { type, error });
    });
    
    // 消息接收事件
    connection.on('message', (message) => {
      if (this.activeConnectionType === type) {
        this.emit('messageReceived', { type, message });
      }
    });
    
    // 连接中事件
    connection.on('connecting', () => {
      this._updateConnectionState(type, ConnectionState.CONNECTING);
    });
  }

  /**
   * 启动连接管理器
   */
  async start() {
    logger.info("[ConnectionManager] Starting connection manager");
    
    // 按优先级尝试连接
    const sortedTypes = Object.keys(this.connectionPriority)
      .sort((a, b) => this.connectionPriority[a] - this.connectionPriority[b]);
    
    for (const type of sortedTypes) {
      if (this.connections.has(type)) {
        try {
          await this._connectWithType(type);
          if (this.activeConnection) {
            break; // 成功连接后停止尝试其他连接
          }
        } catch (error) {
          logger.warn(`[ConnectionManager] Failed to connect with ${type}:`, error);
        }
      }
    }
    
    // 启动健康检查
    this._startHealthCheck();
    
    logger.info("[ConnectionManager] Connection manager started");
  }

  /**
   * 停止连接管理器
   */
  async stop() {
    logger.info("[ConnectionManager] Stopping connection manager");

    // 停止健康检查
    this._stopHealthCheck();

    // 断开所有连接
    for (const [type, connection] of this.connections) {
      try {
        await connection.disconnect();
      } catch (error) {
        logger.error(`[ConnectionManager] Error disconnecting ${type}:`, error);
      }
    }

    this.activeConnection = null;
    this.activeConnectionType = null;

    logger.info("[ConnectionManager] Connection manager stopped");
  }

  /**
   * 发送消息
   * @param {*} message - 要发送的消息
   */
  async sendMessage(message) {
    if (!this.activeConnection) {
      // 如果没有活动连接，将消息加入队列
      this._queueMessage(message);
      logger.warn("[ConnectionManager] No active connection, message queued");
      return false;
    }

    try {
      await this.activeConnection.sendMessage(message);
      return true;
    } catch (error) {
      logger.error("[ConnectionManager] Failed to send message:", error);
      // 发送失败，将消息加入队列并尝试切换连接
      this._queueMessage(message);
      this._handleConnectionError({ type: this.activeConnectionType, error });
      return false;
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    const status = {};
    for (const [type, state] of this.connectionStates) {
      status[type] = {
        state,
        isActive: this.activeConnectionType === type,
        reconnectAttempts: this.reconnectAttempts.get(type) || 0
      };
    }
    return {
      activeConnection: this.activeConnectionType,
      connections: status,
      queuedMessages: this.messageQueue.length
    };
  }

  /**
   * 强制切换到指定连接类型
   * @param {string} type - 连接类型
   */
  async forceSwitch(type) {
    if (!this.connections.has(type)) {
      throw new Error(`Connection type ${type} not registered`);
    }

    if (this.switchingLock) {
      logger.warn("[ConnectionManager] Switch already in progress");
      return false;
    }

    this.switchingLock = true;

    try {
      // 断开当前连接
      if (this.activeConnection && this.activeConnectionType !== type) {
        await this.activeConnection.disconnect();
      }

      // 连接到指定类型
      await this._connectWithType(type);

      return this.activeConnectionType === type;
    } catch (error) {
      logger.error(`[ConnectionManager] Failed to force switch to ${type}:`, error);
      return false;
    } finally {
      this.switchingLock = false;
    }
  }

  /**
   * 更新连接状态
   * @param {string} type - 连接类型
   * @param {string} state - 新状态
   */
  _updateConnectionState(type, state) {
    const oldState = this.connectionStates.get(type);
    if (oldState !== state) {
      this.connectionStates.set(type, state);
      this.emit('connectionStateChanged', { type, oldState, newState: state });
      logger.info(`[ConnectionManager] ${type} connection state: ${oldState} -> ${state}`);
    }
  }

  /**
   * 尝试激活连接
   * @param {string} type - 连接类型
   */
  async _tryActivateConnection(type) {
    if (this.switchingLock) {
      return;
    }

    // 如果没有活动连接，或者新连接优先级更高，则切换
    const shouldSwitch = !this.activeConnection ||
      this.connectionPriority[type] < this.connectionPriority[this.activeConnectionType];

    if (shouldSwitch) {
      await this._switchToConnection(type);
    }
  }

  /**
   * 切换到指定连接
   * @param {string} type - 连接类型
   */
  async _switchToConnection(type) {
    if (this.switchingLock) {
      return;
    }

    this.switchingLock = true;

    try {
      const connection = this.connections.get(type);
      const state = this.connectionStates.get(type);

      if (state !== ConnectionState.CONNECTED) {
        logger.warn(`[ConnectionManager] Cannot switch to ${type}: not connected`);
        return;
      }

      const oldType = this.activeConnectionType;

      // 切换活动连接
      this.activeConnection = connection;
      this.activeConnectionType = type;

      logger.info(`[ConnectionManager] Switched active connection: ${oldType} -> ${type}`);

      // 发送队列中的消息
      await this._flushMessageQueue();

      // 通知前端连接状态变化
      this._notifyConnectionChange(type);

    } catch (error) {
      logger.error(`[ConnectionManager] Error switching to ${type}:`, error);
    } finally {
      this.switchingLock = false;
    }
  }

  /**
   * 处理连接丢失
   * @param {string} type - 连接类型
   */
  async _handleConnectionLost(type) {
    if (this.activeConnectionType === type) {
      logger.warn(`[ConnectionManager] Active connection ${type} lost, trying fallback`);

      // 清除当前活动连接
      this.activeConnection = null;
      this.activeConnectionType = null;

      // 尝试切换到备用连接
      await this._tryFallbackConnection(type);
    }

    // 尝试重连丢失的连接
    this._scheduleReconnect(type);
  }

  /**
   * 尝试备用连接
   * @param {string} excludeType - 要排除的连接类型
   */
  async _tryFallbackConnection(excludeType) {
    const availableTypes = Array.from(this.connections.keys())
      .filter(type => type !== excludeType)
      .sort((a, b) => this.connectionPriority[a] - this.connectionPriority[b]);

    for (const type of availableTypes) {
      const state = this.connectionStates.get(type);
      if (state === ConnectionState.CONNECTED) {
        await this._switchToConnection(type);
        return;
      }
    }

    // 如果没有可用的连接，尝试连接所有备用连接
    for (const type of availableTypes) {
      try {
        await this._connectWithType(type);
        if (this.activeConnection) {
          return;
        }
      } catch (error) {
        logger.warn(`[ConnectionManager] Failed to connect fallback ${type}:`, error);
      }
    }

    logger.error("[ConnectionManager] No fallback connections available");
  }

  /**
   * 使用指定类型连接
   * @param {string} type - 连接类型
   */
  async _connectWithType(type) {
    const connection = this.connections.get(type);
    if (!connection) {
      throw new Error(`Connection ${type} not registered`);
    }

    this._updateConnectionState(type, ConnectionState.CONNECTING);

    try {
      await connection.connect();
      // 连接成功会通过事件处理
    } catch (error) {
      this._updateConnectionState(type, ConnectionState.ERROR);
      throw error;
    }
  }

  /**
   * 安排重连
   * @param {string} type - 连接类型
   */
  _scheduleReconnect(type) {
    const attempts = this.reconnectAttempts.get(type) || 0;

    if (attempts >= this.maxReconnectAttempts) {
      logger.warn(`[ConnectionManager] Max reconnect attempts reached for ${type}`);
      return;
    }

    this.reconnectAttempts.set(type, attempts + 1);

    setTimeout(async () => {
      try {
        logger.info(`[ConnectionManager] Attempting to reconnect ${type} (${attempts + 1}/${this.maxReconnectAttempts})`);
        await this._connectWithType(type);
      } catch (error) {
        logger.error(`[ConnectionManager] Reconnect failed for ${type}:`, error);
        this._scheduleReconnect(type);
      }
    }, this.reconnectInterval);
  }

  /**
   * 将消息加入队列
   * @param {*} message - 消息
   */
  _queueMessage(message) {
    if (this.messageQueue.length >= this.maxQueueSize) {
      this.messageQueue.shift(); // 移除最旧的消息
    }
    this.messageQueue.push({
      message,
      timestamp: Date.now()
    });
  }

  /**
   * 发送队列中的消息
   */
  async _flushMessageQueue() {
    if (!this.activeConnection || this.messageQueue.length === 0) {
      return;
    }

    logger.info(`[ConnectionManager] Flushing ${this.messageQueue.length} queued messages`);

    const messages = [...this.messageQueue];
    this.messageQueue = [];

    for (const { message } of messages) {
      try {
        await this.activeConnection.sendMessage(message);
      } catch (error) {
        logger.error("[ConnectionManager] Failed to send queued message:", error);
        // 重新加入队列
        this._queueMessage(message);
        break;
      }
    }
  }

  /**
   * 启动健康检查
   */
  _startHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(() => {
      this._performHealthCheck();
    }, this.healthCheckInterval);
  }

  /**
   * 停止健康检查
   */
  _stopHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }

  /**
   * 执行健康检查
   */
  async _performHealthCheck() {
    if (!this.activeConnection) {
      return;
    }

    try {
      // 检查活动连接的健康状态
      const isHealthy = await this.activeConnection.isHealthy();
      if (!isHealthy) {
        logger.warn(`[ConnectionManager] Health check failed for ${this.activeConnectionType}`);
        this._handleConnectionError({
          type: this.activeConnectionType,
          error: new Error('Health check failed')
        });
      }
    } catch (error) {
      logger.error(`[ConnectionManager] Health check error for ${this.activeConnectionType}:`, error);
    }
  }

  /**
   * 处理连接状态变化
   * @param {object} event - 状态变化事件
   */
  _handleConnectionStateChange(event) {
    const { type, oldState, newState } = event;

    // 通知前端状态变化
    this._notifyConnectionChange(type, { oldState, newState });

    // 如果是WebSocket连接恢复，且当前使用的是USB连接，则切换回WebSocket
    if (type === ConnectionType.WEBSOCKET &&
        newState === ConnectionState.CONNECTED &&
        this.activeConnectionType === ConnectionType.USB) {

      logger.info("[ConnectionManager] WebSocket recovered, switching back from USB");
      this._tryActivateConnection(type);
    }
  }

  /**
   * 处理连接错误
   * @param {object} event - 错误事件
   */
  _handleConnectionError(event) {
    const { type, error } = event;
    logger.error(`[ConnectionManager] Connection error for ${type}:`, error);

    // 如果是活动连接出错，尝试切换到备用连接
    if (this.activeConnectionType === type) {
      this._tryFallbackConnection(type);
    }
  }

  /**
   * 处理接收到的消息
   * @param {object} event - 消息事件
   */
  _handleMessageReceived(event) {
    const { type, message } = event;

    // 转发消息到前端
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("connection-message", {
        connectionType: type,
        message
      });
    }
  }

  /**
   * 通知前端连接变化
   * @param {string} type - 连接类型
   * @param {object} details - 详细信息
   */
  _notifyConnectionChange(type, details = {}) {
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("connection-status-changed", {
        type,
        isActive: this.activeConnectionType === type,
        status: this.getConnectionStatus(),
        ...details
      });
    }
  }
}

module.exports = { ConnectionManager, ConnectionType, ConnectionState };
