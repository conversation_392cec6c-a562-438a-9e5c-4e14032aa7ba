"use strict";

/**
 * 消息解析工具类
 * @class
 *
 * @example WebSocket消息格式
 * 0x0C 0x10 0x22 0x04 0x14 0x00 0x81 0x02 0x00 0x01 0x50 0x30 0xFC 0xAF 0x7F 0x60 0x95 0x01 0x00 0x00
 *
 * @example MQTT消息格式
 * // Buffer输出
 * <Buffer 0c 10 22 04 14 00 81 02 00 01 50 30 fc af 7f 60 95 01 00 00>
 *
 * // Buffer.toString("hex")字符串输出
 * 0c10220114008102a5014400ac8b3064950100000800440001010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001100000000000080000000000000000
 */
class MessageParser {
  /**
   * 解析消息
   * @param {string} message - 消息内容
   * @returns {object} 解析后的消息对象
   */

  static parse(message, type = "mqtt") {
    try {
      if (type === "mqtt") {
        const data8 = MessageParser.Str2Uint8Array(message.toString("hex"));
        return MessageParser.parseData8(data8);
      } else if (type === "websocket") {
        const cleanHex = message
          .replace(/0x|\n/g, "") // 删除 0x 和换行符
          .replace(/\s+/g, "") // 删除所有空格
          .trim(); // 去除首尾空白
        const data8 = MessageParser.Str2Uint8Array(cleanHex);
        console.log("data8", data8);
        return MessageParser.parseData8(data8);
      } else if (type === "usb") {
        
      }
    } catch (error) {
      console.error("Failed to parse message:", error);
      return null;
    }
  }

  /**
   * 将字符串转换为 Uint8Array
   * @param {string} value - 要转换的字符串
   * @returns {Uint8Array} 转换后的 Uint8Array
   */
  static Str2Uint8Array(value) {
    let str = value.replace(/\s*/g, ""); // 先去除前后空格

    if (str.length === 0) return new Uint8Array(0); // 直接返回空数组
    if (str.length % 2 !== 0) str = "0" + str; // 确保长度为偶数

    const len = str.length / 2;

    if (len < 20) return new Uint8Array(0); // 直接返回空数组

    const data8 = new Uint8Array(len); // 直接分配 Uint8Array

    for (let i = 0; i < len; i++) {
      data8[i] = parseInt(str.substr(i * 2, 2), 16);
    }

    return data8;
  }

  static parseData8(data) {
    const { parseConfig } = require("./messageDict");

    if (!data || data.length < 20) {
      console.error("Invalid data length");
      return null;
    }

    let res = {};
    let flag = 0;

    const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);

    // 解析头部信息
    try {
      res = MessageParser.parseHeader(dataView, parseConfig.head);
      flag = MessageParser.calculateHeaderLength(parseConfig.head);
    } catch (error) {
      console.error("Error parsing header:", error);
      return null;
    }

    // 如果只有头部信息，直接返回
    if (data.byteLength === 20) return res;

    // 解析消息体
    try {
      const bodyOffset = res["headerLength"];
      const id_ = dataView.getUint16(bodyOffset || 0, true);
      const config = parseConfig[`ID${id_}`];

      if (!config) {
        // 未知消息ID
        // console.warn(`Unknown message ID: ${id_}`);
        return res;
      }

      res = {
        ...res,
        ...MessageParser.parseMessageBody(dataView, config, id_, flag),
      };
    } catch (error) {
      console.error("Error parsing message body:", error);
      return res;
    }

    return res;
  }

  static parseHeader(dataView, headerConfig) {
    const res = {};
    let flag = 0;

    for (const item of headerConfig) {
      try {
        res[item.key] = MessageParser.parseValue(dataView, item, flag);
        if (item.key === "id") console.log("parseHeader ID",res[item.key]);
        flag += item.len;
      } catch (error) {
        console.error(`Error parsing header field ${item.key}:`, error);
        throw error;
      }
    }

    return res;
  }

  static parseMessageBody(dataView, config, id_, startFlag) {
    const res = {};
    let flag = startFlag;

    // 通用的消息体解析
    for (const item of config) {
      try {
        if (id_ === 11 && item.key === "otherInfo") {
          res[item.key] = MessageParser.parseStatusInfo(dataView, flag, res["senderStatus"]);
        } else if (id_ === 20 && item.key === "jsonData") {
          res[item.key] = MessageParser.parseJsonData(dataView, flag);
        } else {
          res[item.key] = MessageParser.parseValue(dataView, item, flag);
        }
        flag += item.len;
      } catch (error) {
        console.error(`Error parsing body field ${item.key}:`, error);
        continue;
      }
    }

    return res;
  }

  static parseValue(dataView, item, offset) {
    switch (item.len) {
      case 0:
        return "";
      case 1:
        return dataView.getUint8(offset);
      case 2:
        // 特征位特殊处理
        if (item.key === "featureValue") {
          return String.fromCharCode(dataView.getUint8(0)) + String.fromCharCode(dataView.getUint8(1));
        }
        return dataView.getUint16(offset, true);
      case 4:
        if (item.type === "FLOAT") {
          return dataView.getFloat32(offset, true);
        } else if (item.type === "BIN") {
          return dataView.getUint32(offset, true);
        }
        return dataView.getUint32(offset, true);
      case 8:
        if (item.type === "TIME") {
          const timestamp = dataView.getBigUint64(offset, true);
          return MessageParser.formatTimestamp(parseInt(timestamp.toString()));
        }
        return `${dataView.getUint16(offset, true)},${dataView.getUint16(
          offset + 2,
          true
        )},${dataView.getUint16(offset + 4, true)},${dataView.getUint16(offset + 6, true)}`;
      case 24:
        if (item.key === "extendedGeneralFeatures") {
          const buttons = [];
          // 解析前3字节的12个按钮状态
          for (let i = 0; i < 3; i++) {
            const byte = dataView.getUint8(offset + i);
            for (let j = 0; j < 4; j++) {
              buttons.push((byte >> (j * 2)) & 0x03);
            }
          }
          
          // 获取触摸点数量
          const touchCount = dataView.getUint8(offset + 3);
          
          // 解析5组触摸坐标
          const touchPoints = [];
          for (let i = 0; i < 5; i++) {
            const x = dataView.getUint16(offset + 4 + i * 4, true);
            const y = dataView.getUint16(offset + 4 + i * 4 + 2, true);
            touchPoints.push({ x, y });
          }
          
          return {
            buttons,
            touchCount,
            touchPoints
          };
        }
        return null;
      default:
        return null;
    }
  }

  static parseStatusInfo(dataView, offset, senderStatus) {
    const statusInfo = {};
    const byte1 = dataView.getUint8(offset);
    const byte2 = dataView.getUint8(offset + 1);
    const byte3 = dataView.getUint8(offset + 2);

    switch (senderStatus) {
      case 8: // 初始化
        statusInfo.parameterStatus = !!(byte1 & 0x01);
        statusInfo.modeType = byte1 & 0x02 ? "远程模式" : "本地模式";
        statusInfo.moduleStatus = MessageParser.parseModuleStatus(byte1, byte2);
        break;
      case 32: // 等待
        statusInfo.vehicleTypeSpecified = !!(byte1 & 0x80);
        break;
      case 48: // 登录
        statusInfo.subVehicleType = (byte2 << 8) | byte1;
        break;
      case 64: // 远控
      case 80: // 锁定
      case 88: // 失效保护
        statusInfo.serialInfo = MessageParser.parseSerialInfo(byte1, byte2, byte3);
        break;
    }

    return statusInfo;
  }

  static parseJsonData(dataView, offset) {
    const dataViewOffset = dataView.buffer.slice(offset);
    const decoder = new TextDecoder("utf-8");
    const jsonString = decoder.decode(dataViewOffset);
    return JSON.parse(jsonString);
  }

  static parseModuleStatus(byte1, byte2) {
    const getModuleStatus = (bits) => {
      const statusMap = {
        0: "未连接",
        1: "已连接未装订参数",
        2: "已连接已装订参数",
        3: "已连接已完成初始化",
      };
      return statusMap[bits] || "未知状态";
    };

    return {
      module1: getModuleStatus((byte1 >> 4) & 0x03),
      module2: getModuleStatus((byte1 >> 6) & 0x03),
      module3: getModuleStatus(byte2 & 0x03),
      module4: getModuleStatus((byte2 >> 2) & 0x03),
    };
  }

  static parseSerialInfo(byte1, byte2, byte3) {
    return {
      year: byte1,
      month: byte2,
      number: byte3,
      extended: {
        month: (byte2 >> 4) & 0x0f,
        number: ((byte2 & 0x0f) << 8) | byte3,
      },
    };
  }

  static calculateHeaderLength(headerConfig) {
    return headerConfig.reduce((sum, item) => sum + item.len, 0);
  }

  static formatTimestamp(timestamp) {
    // 检查时间戳是否为秒级，如果是，则转换为毫秒
    if (timestamp.toString().length === 10) {
      timestamp *= 1000;
    }

    const date = new Date(timestamp);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    const milliseconds = String(date.getMilliseconds()).padStart(3, "0"); // 毫秒部分

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }
}

module.exports = MessageParser;
